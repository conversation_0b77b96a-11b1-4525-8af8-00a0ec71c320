<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WDA Monitor - Login</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .login-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            padding: 2rem;
            width: 100%;
            max-width: 400px;
        }
        .login-header {
            text-align: center;
            margin-bottom: 2rem;
        }
        .login-header h2 {
            color: #333;
            font-weight: 600;
        }
        .login-header p {
            color: #666;
            margin-bottom: 0;
        }
        .form-control {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 12px 15px;
            transition: all 0.3s ease;
        }
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        .btn-login {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
            padding: 12px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        .btn-login:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        .alert {
            border-radius: 10px;
        }
        .users-info {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 1rem;
            margin-top: 1.5rem;
            font-size: 0.85rem;
        }
        .users-info h6 {
            color: #495057;
            margin-bottom: 0.5rem;
        }
        .user-credential {
            display: flex;
            justify-content: space-between;
            margin-bottom: 0.25rem;
            padding: 0.25rem 0;
        }
        .user-credential:last-child {
            margin-bottom: 0;
        }
        .username {
            font-weight: 600;
            color: #495057;
        }
        .password {
            color: #6c757d;
            font-family: monospace;
        }
        .role-badge {
            font-size: 0.7rem;
            padding: 0.2rem 0.4rem;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-header">
            <h2><i class="fas fa-chart-line me-2"></i>WDA Monitor</h2>
            <p>Please sign in to continue</p>
        </div>

        <form id="loginForm">
            <div class="mb-3">
                <label for="username" class="form-label">Username</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="fas fa-user"></i></span>
                    <input type="text" class="form-control" id="username" name="username" required>
                </div>
            </div>

            <div class="mb-3">
                <label for="password" class="form-label">Password</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="fas fa-lock"></i></span>
                    <input type="password" class="form-control" id="password" name="password" required>
                </div>
            </div>

            <div class="alert alert-danger d-none" id="loginError">
                Invalid username or password. Please try again.
            </div>

            <div class="alert alert-warning d-none" id="permissionError">
                You don't have sufficient permissions to access that resource.
            </div>

            <div class="alert alert-info d-none" id="adminError">
                Admin access is required to access that resource.
            </div>

            <button type="submit" class="btn btn-primary btn-login w-100">
                <span class="spinner-border spinner-border-sm d-none me-2" id="loginSpinner"></span>
                Sign In
            </button>
        </form>

        <div class="users-info">
            <h6><i class="fas fa-info-circle me-1"></i>Available Users:</h6>
            <div class="user-credential">
                <span class="username">WDA</span>
                <span class="password">admin</span>
                <span class="badge bg-danger role-badge">Admin</span>
            </div>
            <div class="user-credential">
                <span class="username">analyst1</span>
                <span class="password">analyst123</span>
                <span class="badge bg-info role-badge">Analyst</span>
            </div>
            <div class="user-credential">
                <span class="username">analyst2</span>
                <span class="password">analyst456</span>
                <span class="badge bg-info role-badge">Analyst</span>
            </div>
            <div class="user-credential">
                <span class="username">operator1</span>
                <span class="password">operator123</span>
                <span class="badge bg-warning role-badge">Operator</span>
            </div>
            <div class="user-credential">
                <span class="username">operator2</span>
                <span class="password">operator456</span>
                <span class="badge bg-warning role-badge">Operator</span>
            </div>
            <div class="user-credential">
                <span class="username">viewer1</span>
                <span class="password">viewer123</span>
                <span class="badge bg-secondary role-badge">Viewer</span>
            </div>
            <div class="user-credential">
                <span class="username">manager1</span>
                <span class="password">manager123</span>
                <span class="badge bg-success role-badge">Manager</span>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.getElementById('loginForm').addEventListener('submit', async (e) => {
            e.preventDefault();

            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const errorDiv = document.getElementById('loginError');
            const spinner = document.getElementById('loginSpinner');
            const submitBtn = e.target.querySelector('button[type="submit"]');

            // Show loading state
            spinner.classList.remove('d-none');
            submitBtn.disabled = true;
            errorDiv.classList.add('d-none');

            try {
                const response = await fetch('/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ username, password })
                });

                const result = await response.json();

                if (response.ok) {
                    // Store user info in localStorage for client-side reference
                    localStorage.setItem('userInfo', JSON.stringify(result.user));
                    // Redirect to main page
                    window.location.href = '/';
                } else {
                    errorDiv.textContent = result.message || 'Login failed';
                    errorDiv.classList.remove('d-none');
                }
            } catch (error) {
                errorDiv.textContent = 'Network error. Please try again.';
                errorDiv.classList.remove('d-none');
            } finally {
                // Hide loading state
                spinner.classList.add('d-none');
                submitBtn.disabled = false;
            }
        });

        // Check for error messages in URL parameters
        function checkUrlParams() {
            const urlParams = new URLSearchParams(window.location.search);
            const error = urlParams.get('error');

            if (error === 'insufficient_permissions') {
                document.getElementById('permissionError').classList.remove('d-none');
            } else if (error === 'admin_required') {
                document.getElementById('adminError').classList.remove('d-none');
            }
        }

        // Check if already authenticated
        fetch('/check-auth')
            .then(response => response.json())
            .then(data => {
                if (data.authenticated) {
                    window.location.href = '/';
                }
            })
            .catch(error => console.log('Auth check failed:', error));

        // Check URL parameters on page load
        checkUrlParams();
    </script>
</body>
</html>
