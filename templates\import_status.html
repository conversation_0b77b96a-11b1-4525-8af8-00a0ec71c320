<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Import Status Check</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">
    <style>
        .table th {
            background-color: #f8f9fa;
        }
        .table td {
            vertical-align: middle;
        }
        .bg-light {
            background-color: #e9ecef !important;
        }
    </style>
</head>
<body>
    <!-- Navigation Bar -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="/">WDA Monitor</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link" href="/">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/visuals">Visualizations</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/wda-reg-monitor">WDA_Reg Monitor</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="/import-status">Import Status</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <div class="card animate__animated animate__fadeInUp">
                    <div class="card-header">
                        <h4>Import Status Check</h4>
                    </div>
                    <div class="card-body">
                        <!-- Date Selection Section -->
                        <div class="row mb-3">
                            <div class="col-md-5">
                                <label for="startDate" class="form-label">Start Date</label>
                                <input type="date" class="form-control" id="startDate">
                            </div>
                            <div class="col-md-5">
                                <label for="endDate" class="form-label">End Date</label>
                                <input type="date" class="form-control" id="endDate">
                            </div>
                            <div class="col-md-2 d-flex align-items-end">
                                <button class="btn btn-primary w-100" id="checkImportStatus">
                                    Check Import Status
                                </button>
                            </div>
                        </div>

                        <!-- Status Message and Loading -->
                        <div id="statusMessage" class="alert" style="display: none;"></div>
                        <div id="loading" style="display: none;">
                            <div class="d-flex justify-content-center">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">Loading...</span>
                                </div>
                            </div>
                        </div>

                        <!-- Stage Buttons and Report Dates -->
                        <div class="row mt-3">
                            <!-- Separate the Download Not Approved button into its own section -->
                            <div class="col-12">
                                <div class="card">
                                    <div class="card-header">
                                        <h5 class="card-title mb-0">Not Approved Parts</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-12">
                                                <button class="btn btn-info w-100" id="downloadNotApproved">
                                                    Download Latest Not Approved Parts
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="col-12">
                                <button class="btn btn-primary w-100 mb-2" id="downloadReports">
                                    Stage 1: Download Reports
                                </button>
                            </div>
                            <div class="col-12">
                                <button class="btn btn-primary w-100 mb-2" id="downloadPartDetails">
                                    Stage 2: Download Part Details
                                </button>
                            </div>
                            <div class="col-12">
                                <div class="card mb-2">
                                    <div class="card-header d-flex justify-content-between align-items-center">
                                        Available Report Dates
                                        <button class="btn btn-sm btn-outline-secondary" id="refreshDates">
                                            <i class="fas fa-sync-alt"></i> Refresh
                                        </button>
                                    </div>
                                    <div class="card-body">
                                        <select multiple class="form-control" id="reportDates" size="5"></select>
                                    </div>
                                </div>
                            </div>
                            <div class="col-12">
                                <button class="btn btn-primary w-100 mb-2" id="calculateStatus" >
                                    Stage 3: Calculate Status
                                </button>
                            </div>
                        </div>

                        <!-- Results Section -->
                        <div id="resultsSection" style="display: none;">
                            <h5 class="mt-4">Results Summary</h5>
                            <div class="table-responsive">
                                <table class="table table-bordered table-hover">
                                    <thead>
                                        <tr>
                                            <th>Date</th>
                                            <th>Table</th>
                                            <th>Total Parts</th>
                                            <th>Received</th>
                                            <th>Imported</th>
                                            <th>Not Imported</th>
                                            <th>In Progress</th>
                                            <th>Not Received</th>
                                        </tr>
                                    </thead>
                                    <tbody id="resultsTableBody">
                                    </tbody>
                                </table>
                            </div>

                            <!-- Status Summary Table -->
                            <div class="mt-4">
                                <h5>Status Summary by Date</h5>
                                <div class="table-responsive">
                                    <table class="table table-bordered table-hover">
                                        <thead>
                                            <tr>
                                                <th rowspan="2">Date</th>
                                                <th colspan="2" class="text-center bg-light">Found Parts</th>
                                                <th colspan="2" class="text-center bg-light">Not Found Parts</th>
                                            </tr>
                                            <tr>
                                                <th class="text-center">In Progress</th>
                                                <th class="text-center">Not Received</th>
                                                <th class="text-center">In Progress</th>
                                                <th class="text-center">Not Received</th>
                                            </tr>
                                        </thead>
                                        <tbody id="statusSummaryTableBody">
                                        </tbody>
                                    </table>
                                </div>
                            </div>



                            <!-- Original Download Buttons for reports -->
                            <div class="row mt-3">
                                <div class="col-6">
                                    <button class="btn btn-success w-100" id="downloadImported" disabled>
                                        Download Imported Report
                                    </button>
                                </div>
                                <div class="col-6">
                                    <button class="btn btn-warning w-100" id="downloadMissed" disabled>
                                        Download Missed Report
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function showMessage(message, type) {
            const statusDiv = $('#statusMessage');
            statusDiv.removeClass().addClass(`alert alert-${type}`).text(message).show();
        }

        function populateResultsTable(results) {
            const tbody = $('#resultsTableBody');
            tbody.empty();

            results.forEach(row => {
                tbody.append(`
                    <tr>
                        <td>${row.date}</td>
                        <td>${row.table}</td>
                        <td>${row.total_parts}</td>
                        <td>${row.received_count}</td>
                        <td>${row.imported_count}</td>
                        <td>${row.not_imported_count}</td>
                        <td>${row.in_progress_count}</td>
                        <td>${row.not_received_count}</td>
                    </tr>
                `);
            });
        }

        function populateStatusSummaryTable(results) {
            const tbody = $('#statusSummaryTableBody');
            tbody.empty();

            // Group results by date
            const groupedByDate = {};
            results.forEach(row => {
                if (!groupedByDate[row.date]) {
                    groupedByDate[row.date] = {
                        in_progress_found: 0,
                        not_received_found: 0,
                        in_progress_not_found: 0,
                        not_received_not_found: 0
                    };
                }

                if (row.table === 'found') {
                    groupedByDate[row.date].in_progress_found = row.in_progress_count || 0;
                    groupedByDate[row.date].not_received_found = row.not_received_count || 0;
                } else if (row.table === 'notfound') {
                    groupedByDate[row.date].in_progress_not_found = row.in_progress_count || 0;
                    groupedByDate[row.date].not_received_not_found = row.not_received_count || 0;
                }
            });

            // Add rows to table
            Object.entries(groupedByDate).forEach(([date, counts]) => {
                tbody.append(`
                    <tr>
                        <td>${date}</td>
                        <td class="text-center">${counts.in_progress_found}</td>
                        <td class="text-center">${counts.not_received_found}</td>
                        <td class="text-center">${counts.in_progress_not_found}</td>
                        <td class="text-center">${counts.not_received_not_found}</td>
                    </tr>
                `);
            });
        }

        function downloadFile(url) {
            window.location.href = url;
        }

        $(document).ready(function() {
            // New function to load available dates
            async function loadAvailableDates() {
                try {
                    const response = await fetch('/get-available-dates');
                    const data = await response.json();
                    if (response.ok) {
                        const select = $('#reportDates');
                        select.empty();
                        data.dates.forEach(date => {
                            select.append(`<option value="${date}">${date}</option>`);
                        });
                        // Enable calculate status button if dates are available
                        if (data.dates.length > 0) {
                            $('#calculateStatus').prop('disabled', false);
                        }
                    } else {
                        showMessage(data.error || 'Failed to load available dates', 'warning');
                    }
                } catch (error) {
                    showMessage('Error loading available dates', 'danger');
                }
            }

            // Load available dates when page loads
            loadAvailableDates();

            $('#checkImportStatus').click(async function() {
                const startDate = $('#startDate').val();
                const endDate = $('#endDate').val();

                if (!startDate || !endDate) {
                    showMessage('Please select both start and end dates', 'warning');
                    return;
                }

                $('#loading').show();
                $('#resultsSection').hide();
                $(this).prop('disabled', true);
                $('#downloadImported').prop('disabled', true);
                $('#downloadMissed').prop('disabled', true);

                try {
                    const response = await fetch('/run-import-status', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            start_date: startDate,
                            end_date: endDate
                        })
                    });

                    const data = await response.json();
                    if (response.ok) {
                        showMessage('Import status check completed successfully!', 'success');
                        console.log('Import status check completed successfully!');
                        populateResultsTable(data.results);
                        populateStatusSummaryTable(data.results);
                        $('#resultsSection').show();
                        $('#downloadImported').prop('disabled', false);
                        $('#downloadMissed').prop('disabled', false);
                    } else {
                        showMessage(data.error || 'An error occurred', 'danger');
                    }
                } catch (error) {
                    showMessage('An error occurred while processing the request', 'danger');
                } finally {
                    $('#loading').hide();
                    $(this).prop('disabled', false);
                }
            });

            $('#downloadImported').click(function() {
                const startDate = $('#startDate').val();
                const endDate = $('#endDate').val();
                downloadFile(`/download-report/imported/${startDate}/${endDate}`);
            });

            $('#downloadMissed').click(function() {
                const startDate = $('#startDate').val();
                const endDate = $('#endDate').val();
                downloadFile(`/download-report/missed/${startDate}/${endDate}`);
            });

            $('#downloadNotApproved').click(async function() {
                try {
                    const button = $(this);
                    const originalText = button.html();

                    // Update button state
                    button.prop('disabled', true);
                    button.html('<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Downloading...');

                    showMessage('Preparing not approved file for download...', 'info');

                    const response = await fetch('/download-not-approved', {
                        method: 'GET'
                    });

                    if (response.ok) {
                        const blob = await response.blob();
                        const url = window.URL.createObjectURL(blob);
                        const a = document.createElement('a');
                        a.href = url;
                        a.download = 'Latest_Not_Approved.7z';
                        document.body.appendChild(a);
                        a.click();
                        window.URL.revokeObjectURL(url);
                        document.body.removeChild(a);
                        showMessage('Not approved file downloaded successfully', 'success');
                    } else {
                        const error = await response.json();
                        showMessage(error.error || 'Failed to download not approved file', 'danger');
                    }
                } catch (error) {
                    console.error('Error:', error);
                    showMessage('Error downloading not approved file', 'danger');
                } finally {
                    // Reset button state
                    const button = $('#downloadNotApproved');
                    button.prop('disabled', false);
                    button.html('Download Latest Not Approved Parts');
                }
            });

            $('#downloadReports').click(async function() {
                const startDate = $('#startDate').val();
                const endDate = $('#endDate').val();

                if (!startDate || !endDate) {
                    showMessage('Please select both start and end dates', 'warning');
                    return;
                }

                $(this).prop('disabled', true);
                $('#loading').show();

                try {
                    const response = await fetch('/download-reports', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            start_date: startDate,
                            end_date: endDate
                        })
                    });

                    const data = await response.json();
                    if (response.ok) {
                        showMessage('Reports downloaded successfully!', 'success');

                        // Populate the dates dropdown
                        const select = $('#reportDates');
                        select.empty();
                        data.dates.forEach(date => {
                            select.append(`<option value="${date}">${date}</option>`);
                        });
                    } else {
                        showMessage(data.error || 'An error occurred', 'danger');
                    }
                } catch (error) {
                    showMessage('An error occurred while downloading reports', 'danger');
                } finally {
                    $('#loading').hide();
                    $(this).prop('disabled', false);
                }
            });

            $('#downloadPartDetails').click(async function() {
                const startDate = $('#startDate').val();
                const endDate = $('#endDate').val();

                $(this).prop('disabled', true);
                $('#loading').show();

                try {
                    const response = await fetch('/download-part-details', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            start_date: startDate,
                            end_date: endDate
                        })
                    });

                    const data = await response.json();
                    if (response.ok) {
                        showMessage('Part details downloaded successfully!', 'success');
                        $('#calculateStatus').prop('disabled', false);
                    } else {
                        showMessage(data.error || 'An error occurred', 'danger');
                    }
                } catch (error) {
                    showMessage('An error occurred while downloading part details', 'danger');
                } finally {
                    $('#loading').hide();
                    $(this).prop('disabled', false);
                }
            });

            // Modify the calculate status click handler
            $('#calculateStatus').click(async function() {
                const selectedDates = $('#reportDates').val();

                if (!selectedDates || selectedDates.length === 0) {
                    showMessage('Please select at least one date', 'warning');
                    return;
                }

                $(this).prop('disabled', true);
                $('#loading').show();

                try {
                    const response = await fetch('/calculate-status', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            dates: selectedDates
                        })
                    });

                    const data = await response.json();
                    if (response.ok) {
                        showMessage('Status calculation completed successfully!', 'success');
                        populateResultsTable(data.results);
                        populateStatusSummaryTable(data.results);
                        $('#resultsSection').show();
                        $('#downloadImported').prop('disabled', false);
                        $('#downloadMissed').prop('disabled', false);
                    } else {
                        showMessage(data.error || 'An error occurred', 'danger');
                    }
                } catch (error) {
                    showMessage('An error occurred while calculating status', 'danger');
                } finally {
                    $('#loading').hide();
                    $(this).prop('disabled', false);
                }
            });

            // Add refresh button handler
            $('#refreshDates').click(function() {
                loadAvailableDates();
            });
        });
    </script>
</body>
</html>






