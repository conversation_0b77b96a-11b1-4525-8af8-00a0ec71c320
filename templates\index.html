<!-- Main HTML structure for WDA Monitor application -->
<!DOCTYPE html>
<html lang="en">
<head>
	<!-- Meta tags for proper character encoding and responsive viewport -->
	<meta charset="UTF-8">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<title>WDA Monitor</title>

	<!-- Essential CSS Dependencies -->
	<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
	<link href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css" rel="stylesheet">
	<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">

	<!-- Essential JavaScript Dependencies -->
	<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
	<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
	<script src="https://cdnjs.cloudflare.com/ajax/libs/PapaParse/5.3.0/papaparse.min.js"></script>
	<script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
	<!-- Custom CSS Styles -->

	<style>
		.badge {
			font-size: 0.85rem;
			padding: 0.35em 0.65em;
			font-weight: 500;
		}
		.badge.bg-danger {
			background-color: #dc3545 !important;
		}
		.badge.bg-primary {
			background-color: #0d6efd !important;
		}
		.badge.bg-secondary {
			background-color: #6c757d !important;
		}
		.status-badge {
			min-width: 90px;
			text-align: center;
		}
		.file-list {
			max-height: 400px;
			overflow-y: auto;
			overflow-x: auto;
		}
		.table {
			margin-bottom: 0;
			white-space: nowrap;
		}
		.table th {
			background-color: #f8f9fa;
			border-bottom: 2px solid #dee2e6;
			position: sticky;
			top: 0;
			z-index: 1;
		}
		.table td {
			vertical-align: middle;
		}
		.table tr:hover {
			background-color: rgba(0,0,0,.03);
		}
		.table td:nth-child(3) {
			white-space: normal;
			min-width: 200px;
		}

		.stop-date-input {
			max-width: 350px;
		}
		.input-group .btn {
			z-index: 0;
		}

		.expired-file {
			text-decoration: line-through;
			color: #6c757d;
		}

		.chart-loading {
			position: absolute;
			top: 0;
			left: 0;
			right: 0;
			bottom: 0;
			background: rgba(255, 255, 255, 0.8);
			display: none;
			align-items: center;
			justify-content: center;
			z-index: 10;
		}

		.position-relative {
			position: relative;
		}

		.chart-container {
			min-height: 400px;
			width: 100%;
			background: white;
			border-radius: 8px;
			box-shadow: 0 2px 4px rgba(0,0,0,0.05);
			padding: 15px;
		}

		.container-fluid {
			padding: 0 2rem;
			width: 100%;
			max-width: 1400px;
		}
		.navbar {
			box-shadow: 0 2px 4px rgba(0,0,0,0.1);
		}
		.navbar-brand, .nav-link {
			font-weight: 500;
		}
		.nav-link {
			transition: color 0.3s ease;
		}
		.nav-link:hover {
			color: rgba(255,255,255,0.9) !important;
		}
		.card {
			margin-bottom: 2rem;
			border-radius: 0.5rem;
			box-shadow: 0 2px 4px rgba(0,0,0,0.05);
		}
		.card-header {
			background-color: #f8f9fa;
			border-bottom: 1px solid rgba(0,0,0,0.05);
			padding: 1rem 1.5rem;
		}
		.card-body {
			padding: 1.5rem;
		}
		.file-list {
			max-height: 400px;
			overflow-y: auto;
		}
		.loading {
			position: fixed;
			top: 0;
			left: 0;
			right: 0;
			bottom: 0;
			background: rgba(255,255,255,0.9);
			display: none;
			align-items: center;
			justify-content: center;
			z-index: 2000;
		}
		#notification {
			position: fixed;
			top: 20px;
			right: 20px;
			z-index: 1000;
			display: none;
		}
		.btn:disabled {
			cursor: not-allowed;
			opacity: 0.6;
		}
		th[data-sort] {
			cursor: pointer;
		}
		th[data-sort]:hover {
			background-color: #e9ecef;
		}
		.sort-icon {
			display: inline-block;
			margin-left: 5px;
			font-size: 0.8em;
		}
	</style>

</head>
<body>
	<div id="notification" class="alert alert-success animate__animated animate__fadeInRight"></div>
	<div class="loading">
		<div class="spinner-border text-primary" role="status">
			<span class="visually-hidden">Loading...</span>
		</div>
	</div>

	<!-- Navigation Bar -->
	<nav class="navbar navbar-expand-lg navbar-dark bg-dark">
		<div class="container-fluid">
			<a class="navbar-brand" href="/">
				<i class="fas fa-chart-line me-2"></i>WDA Monitor
			</a>
			<button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
				<span class="navbar-toggler-icon"></span>
			</button>
			<div class="collapse navbar-collapse" id="navbarNav">
				<ul class="navbar-nav me-auto">
					<li class="nav-item">
						<a class="nav-link active" href="/">Home</a>
					</li>
					<li class="nav-item">
						<a class="nav-link" href="/visuals">Visualizations</a>
					</li>
					<li class="nav-item">
						<a class="nav-link" href="/wda-reg-monitor">WDA_Reg Monitor</a>
					</li>
					<li class="nav-item">
						<a class="nav-link" href="/import-status">Import Status</a>
					</li>
				</ul>
				<ul class="navbar-nav">
					<li class="navbar-text me-3">
						<i class="fas fa-user me-1"></i>
						<span id="userFullName">{{ user.full_name if user else 'User' }}</span>
						<span class="badge bg-light text-dark ms-1" id="userRole">{{ user.role if user else 'Unknown' }}</span>
					</li>
					{% if user and user.role == 'admin' %}
					<li class="nav-item">
						<a class="nav-link" href="/admin/dashboard">
							<i class="fas fa-cog me-1"></i>Admin
						</a>
					</li>
					{% endif %}
					<li class="nav-item">
						<a class="nav-link" href="#" onclick="logout()">
							<i class="fas fa-sign-out-alt me-1"></i>Logout
						</a>
					</li>
				</ul>
			</div>
		</div>
	</nav>



	<div class="container-fluid mt-5">
		<div class="text-center mb-5">
			<h1 class="display-4 animate__animated animate__fadeIn">WDA Monitor</h1>
		</div>

		<div class="row justify-content-center">
			<div class="col-md-10">
				<!-- File Upload Section -->
				<div class="card mb-4 animate__animated animate__fadeInUp">
					<div class="card-header">Upload File</div>
					<div class="card-body">
						<form id="uploadForm" enctype="multipart/form-data">
							<div class="mb-3">
								<input type="file" class="form-control" id="fileInput" accept=".txt">
							</div>
							<button type="submit" class="btn btn-primary">
								<span class="spinner-border spinner-border-sm d-none" role="status" aria-hidden="true"></span>
								Upload
							</button>
						</form>
					</div>
				</div>

				<!-- File List Section -->
				<div class="card mb-4 animate__animated animate__fadeInUp" style="animation-delay: 0.2s">
					<div class="card-header d-flex justify-content-between align-items-center">
						<span>Files</span>
						<button id="refreshList" class="btn btn-sm btn-outline-secondary">
							<i class="fas fa-sync-alt"></i> Refresh
						</button>
					</div>
					<div class="card-body">
						<div class="file-list">
							<table class="table table-hover" id="filesTable">
								<thead>
									<tr>
										<th>Select</th>
										<th data-sort="id">ID <span class="sort-icon">↕</span></th>
										<th data-sort="file_name">File Name <span class="sort-icon">↕</span></th>
										<th data-sort="upload_date">Upload Date <span class="sort-icon">↕</span></th>
										<th data-sort="last_check_date">Last Check Date <span class="sort-icon">↕</span></th>
										<th data-sort="stop_monitor_date">Stop Monitor Date <span class="sort-icon">↕</span></th>
										<th data-sort="status">Status <span class="sort-icon">↕</span></th>
										<th>Actions</th>
										<th>Upload to Amazon</th>
										<th>Schedule Upload</th>
									</tr>
								</thead>
								<tbody id="fileList">
									{% for file in files %}
									<tr class="animate__animated animate__fadeIn">
										<td>
											<input class="form-check-input file-checkbox" type="checkbox" value="{{ file }}" id="file-{{ loop.index }}">
										</td>
										{% set db_file = db_files|selectattr("file_name", "equalto", file)|first|default(None) %}
										<td>{{ db_file.id if db_file else '-' }}</td>
										<td>
											<label class="form-check-label
												{% if db_file and db_file.stop_monitor_date and db_file.stop_monitor_date|string != 'NaT'
												and db_file.stop_monitor_date.date() < today %} expired-file {% endif %}"
												for="file-{{ loop.index }}">{{ file }}
											</label>
										</td>
										<td>{{ db_file.upload_date.date()  if db_file and db_file.upload_date else 'Not uploaded' }}</td>
										<td>{{ db_file.last_check_date.date()  if db_file and db_file.last_check_date else 'Not checked' }}</td>
										<td>
											<div class="input-group">
												<input type="date" class="form-control form-control-sm stop-date-input"
													value="{{ db_file.stop_monitor_date.date()  if db_file and db_file.stop_monitor_date else '' }}"
													data-filename="{{ file }}">
												<button class="btn btn-outline-primary btn-sm update-stop-date" type="button">Update</button>
											</div>
										</td>
										<td>
											{% if db_file and db_file.stop_monitor_date and db_file.stop_monitor_date|string != 'NaT'
											and db_file.stop_monitor_date.date() < today %}
												<span class="badge bg-danger">Expired</span>
											{% else %}
												<span class="badge bg-secondary status-badge" data-filename="{{ file }}">Idle</span>
											{% endif %}
										</td>
										<td>
											<button class="btn btn-danger btn-sm delete-btn" data-filename="{{ file }}">Delete</button>
										</td>
										<td>
											<button class="btn btn-primary btn-sm upload-amazon-btn" data-filename="{{ file }}">Upload to Amazon</button>
										</td>
										<td>
											<div class="form-check form-switch">
												<input class="form-check-input schedule-upload-checkbox" type="checkbox"
													id="scheduleUpload-{{ loop.index }}" data-filename="{{ file }}"
													{% if file in scheduled_files %}checked{% endif %}>
												<label class="form-check-label" for="scheduleUpload-{{ loop.index }}">
													Weekly Upload
												</label>
											</div>
										</td>
									</tr>
									{% endfor %}
								</tbody>
							</table>
						</div>
					</div>
				</div>

				<!-- Status Check Section -->
				<div class="card mb-4 animate__animated animate__fadeInUp" style="animation-delay: 0.4s">
					<div class="card-header">Status Check</div>
					<div class="card-body">
						<div class="form-check mb-3">
							<input class="form-check-input" type="checkbox" id="ignoreDate" checked>
							<label class="form-check-label" for="ignoreDate">
								Ignore Date
							</label>
						</div>
						<button id="checkStatus" class="btn btn-primary me-2">Check Status</button>
						<button id="checkValidParts" class="btn btn-warning me-2">Check Valid Parts</button>
						<button id="downloadResults" class="btn btn-success">Download Results</button>
					</div>
				</div>
			</div>
		</div>
	</div>




					</div>


</div>



</div>

		</div>
	</div>
	<script>
		let csvData = [];

		// Status polling function
		function pollFileStatus() {
			fetch('/get-file-status')
				.then(response => response.json())
				.then(statusData => {
					// Update status badges
					document.querySelectorAll('.status-badge').forEach(badge => {
						const fileName = badge.dataset.filename;
						if (statusData[fileName]) {
							const status = statusData[fileName];
							let badgeClass = 'bg-secondary';
							if (status === 'In Progress' || status === 'Checking Status') {
								badgeClass = 'bg-primary';
							}
							badge.className = `badge ${badgeClass} status-badge`;
							badge.textContent = status;
						}
					});
				})
				.catch(error => console.error('Error polling status:', error));
		}

		// Start polling when page loads
		let statusPollInterval;
		document.addEventListener('DOMContentLoaded', () => {
			statusPollInterval = setInterval(pollFileStatus, 2000); // Poll every 2 seconds
		});

		// Cleanup on page unload
		window.addEventListener('beforeunload', async (event) => {
			// Clear polling interval
			clearInterval(statusPollInterval);

			// Reset status for all files
			const files = Array.from(document.querySelectorAll('.status-badge')).map(badge => badge.dataset.filename);
			if (files.length > 0) {
				try {
					await fetch('/reset-file-status', {
						method: 'POST',
						headers: {
							'Content-Type': 'application/json'
						},
						body: JSON.stringify({ files })
					});
				} catch (error) {
					console.error('Error resetting file status:', error);
				}
			}
		});

		function showNotification(message, type = 'success') {
			const notification = document.getElementById('notification');
			notification.className = `alert alert-${type} animate__animated animate__fadeInRight`;
			notification.textContent = message;
			notification.style.display = 'block';
			setTimeout(() => {
				notification.classList.remove('animate__fadeInRight');
				notification.classList.add('animate__fadeOutRight');
				setTimeout(() => {
					notification.style.display = 'none';
				}, 500);
			}, 3000);
		}

		function showLoading() {
			document.querySelector('.loading').style.display = 'flex';
		}

		function hideLoading() {
			document.querySelector('.loading').style.display = 'none';
		}



		// Authentication functions
		async function checkAuth() {
			try {
				const response = await fetch('/check-auth');
				const data = await response.json();

				if (data.authenticated) {
					// Update user info in navbar
					document.getElementById('userFullName').textContent = data.user.full_name;
					document.getElementById('userRole').textContent = data.user.role;

					// Store user info for client-side reference
					localStorage.setItem('userInfo', JSON.stringify(data.user));

					return true;
				} else {
					// Redirect to login page
					window.location.replace('/login');
					return false;
				}
			} catch (error) {
				console.error('Auth check failed:', error);
				window.location.replace('/login');
				return false;
			}
		}

		// Global error handler for authentication errors
		function handleAuthError(response) {
			if (response.status === 401) {
				// Authentication required - redirect to login
				window.location.replace('/login');
				return true;
			} else if (response.status === 403) {
				// Permission denied - show error and redirect
				showNotification('You do not have permission to perform this action', 'danger');
				setTimeout(() => {
					window.location.replace('/login?error=insufficient_permissions');
				}, 2000);
				return true;
			}
			return false;
		}

		async function logout() {
			try {
				const response = await fetch('/logout', { method: 'POST' });
				if (response.ok) {
					localStorage.removeItem('userInfo');
					window.location.replace('/login');
				}
			} catch (error) {
				console.error('Logout failed:', error);
				// Force redirect even if logout request fails
				localStorage.removeItem('userInfo');
				window.location.replace('/login');
			}
		}

		// Event Listeners
		document.addEventListener('DOMContentLoaded', async () => {
			const isAuthenticated = await checkAuth();
			if (isAuthenticated) {
				// Start status polling
				statusPollInterval = setInterval(pollFileStatus, 2000);
				// Get scheduled files
				updateScheduledFiles();
			}
		});

		// Event handler for schedule upload checkboxes
		document.addEventListener('change', (e) => {
			if (e.target.classList.contains('schedule-upload-checkbox')) {
				const fileName = e.target.dataset.filename;
				const isScheduled = e.target.checked;

				fetch('/update-schedule-upload', {
					method: 'POST',
					headers: {
						'Content-Type': 'application/json'
					},
					body: JSON.stringify({
						file_name: fileName,
						scheduled: isScheduled
					})
				})
				.then(response => response.json())
				.then(data => {
					if (data.status === 'success') {
						showNotification(isScheduled ?
							`Weekly upload scheduled for ${fileName}` :
							`Weekly upload canceled for ${fileName}`,
							'success');
						// Update local list of scheduled files
						updateScheduledFiles();
					} else {
						showNotification(data.error || 'Failed to update schedule', 'danger');
						// Revert checkbox state
						e.target.checked = !isScheduled;
					}
				})
				.catch(error => {
					console.error('Error updating schedule:', error);
					showNotification('Error updating schedule', 'danger');
					// Revert checkbox state
					e.target.checked = !isScheduled;
				});
			}
		});



		// File operations event listeners

		// Global variables to track status
		let isAmazonUploading = false;
		let scheduledFiles = [];

		// Function to update scheduled files
		function updateScheduledFiles() {
			fetch('/get-scheduled-files')
				.then(response => response.json())
				.then(data => {
					scheduledFiles = data.files || [];
					// Update checkboxes based on server data
					document.querySelectorAll('.schedule-upload-checkbox').forEach(checkbox => {
						const fileName = checkbox.dataset.filename;
						checkbox.checked = scheduledFiles.includes(fileName);
					});
				})
				.catch(error => console.error('Error fetching scheduled files:', error));
		}

		// Function to update Amazon upload button state across all instances
		function updateAmazonButtonState(disabled) {
			const amazonButtons = document.querySelectorAll('.upload-amazon-btn');
			amazonButtons.forEach(btn => {
				btn.disabled = disabled;
				if (disabled) {
					btn.innerHTML = '<i class="fas fa-sync fa-spin"></i> Uploading...';
				} else {
					btn.innerHTML = '<i class="fas fa-cloud-upload-alt"></i> Upload to Amazon';
				}
			});
		}

		document.addEventListener('click', async (e) => {
			if (e.target.classList.contains('upload-amazon-btn') ||
				(e.target.parentElement && e.target.parentElement.classList.contains('upload-amazon-btn'))) {

				// Show confirmation dialog
				if (!confirm('Are you sure you want to upload this file to Amazon?')) {
					return;  // If user clicks Cancel, stop here
				}

				if (isAmazonUploading) {
					showNotification('Another file is currently being uploaded to Amazon. Please wait.', 'warning');
					return;
				}

				const btn = e.target.classList.contains('upload-amazon-btn') ? e.target : e.target.parentElement;
				const fileName = btn.dataset.filename;
				console.log(fileName)
				try {
					isAmazonUploading = true;
					updateAmazonButtonState(true);

					const response = await fetch('/upload-to-amazon', {
						method: 'POST',
						headers: {
							'Content-Type': 'application/json',
						},

						body: JSON.stringify({ file_name: fileName })
					});

					const data = await response.json();

					if (response.ok) {
						showNotification(data.message || 'File uploaded to Amazon successfully', 'success');
					} else {
						throw new Error(data.error || 'Failed to upload file to Amazon');
					}
				} catch (error) {
					showNotification(error.message, 'danger');
				} finally {
					isAmazonUploading = false;
					updateAmazonButtonState(false);
				}
			}
		});

		// Upload to Amazon functionality
		/*document.querySelectorAll('.upload-amazon-btn').forEach(button => {
			button.addEventListener('click', async () => {
				const filename = button.dataset.filename;
				console.log(filename)
				try {
					showLoading();
					const response = await fetch(`/upload-to-amazon`, {
						method: 'POST',
						headers: {
							'Content-Type': 'application/json'
						},
						body: JSON.stringify({ file_name: filename })
					});
					const result = await response.json();

					if (response.ok) {
						showNotification('File uploaded to Amazon successfully');
					} else {
						showNotification(result.error, 'danger');
					}
				} catch (error) {
					showNotification('Error uploading file to Amazon', 'danger');
				} finally {
					hideLoading();
				}
			});
		});*/

		document.getElementById('uploadForm').addEventListener('submit', async (e) => {
			e.preventDefault();
			const formData = new FormData();
			const fileInput = document.getElementById('fileInput');
			const submitBtn = e.target.querySelector('button[type="submit"]');
			formData.append('file', fileInput.files[0]);

			submitBtn.disabled = true; // Disable the button during upload
			try {
				const spinner = submitBtn.querySelector('.spinner-border');
				spinner.classList.remove('d-none');
				showLoading();

				const response = await fetch('/upload', {
					method: 'POST',
					body: formData
				});
				const result = await response.json();

				if (response.ok) {
					showNotification('File uploaded successfully');
					location.reload();
				} else {
					showNotification(result.error, 'danger');
				}
			} catch (error) {
				showNotification('Error uploading file', 'danger');
			} finally {
				submitBtn.disabled = false; // Re-enable the button after upload
				const spinner = submitBtn.querySelector('.spinner-border');
				spinner.classList.add('d-none');
				hideLoading();
			}
		});

		document.querySelectorAll('.delete-btn').forEach(button => {
			console.log("deleting")
			button.addEventListener('click', async () => {
				const filename = button.dataset.filename;
				if (confirm(`Delete ${filename}?`)) {
					try {
						showLoading();
						const response = await fetch(`/delete/${filename}`, {
							method: 'POST'
						});
						const result = await response.json();

						if (response.ok) {
							button.closest('tr').classList.add('animate__fadeOutRight');
							setTimeout(() => {
								location.reload();
							}, 500);
							showNotification('File deleted successfully');
						} else {
							showNotification(result.error, 'danger');
						}
					} catch (error) {
						showNotification('Error deleting file', 'danger');
					} finally {
						hideLoading();
					}
				}
			})
		});









		// Function to handle data updates






		// Refresh list handler
		document.getElementById('refreshList').addEventListener('click', async () => {
			try {
				showLoading();
				const [filesResponse, dbFilesResponse, statusResponse, scheduledFilesResponse] = await Promise.all([
					fetch('/refresh-files'),
					fetch('/get-db-files'),
					fetch('/get-file-status'),
					fetch('/get-scheduled-files')
				]);

				const filesResult = await filesResponse.json();
				const dbFilesResult = await dbFilesResponse.json();
				const statusData = await statusResponse.json();
				const scheduledFilesData = await scheduledFilesResponse.json();

				// Update the global scheduledFiles array
				scheduledFiles = scheduledFilesData.files || [];

				if (filesResponse.ok && dbFilesResult.status === 'success') {
					const fileList = document.getElementById('fileList');
					fileList.innerHTML = '';

					filesResult.files.forEach((file, index) => {
						const dbFileInfo = dbFilesResult.files.find(dbFile => dbFile.file_name === file);
						const currentStatus = statusData[file] || 'Idle';
						const isScheduled = scheduledFiles.includes(file);
						const row = document.createElement('tr');
						row.className = 'animate__animated animate__fadeIn';
						row.innerHTML = `
							<td>
								<input class="form-check-input file-checkbox" type="checkbox" value="${file}" id="file-${index}">
							</td>
							<td>${dbFileInfo ? dbFileInfo.id : '-'}</td>
							<td>
								<label class="form-check-label ${dbFileInfo && dbFileInfo.stop_monitor_date ? (new Date(dbFileInfo.stop_monitor_date) < new Date().setHours(0,0,0,0) ? 'expired-file' : '') : ''}" for="file-${index}">${file}</label>
							</td>
							<td>${dbFileInfo && dbFileInfo.upload_date ? new Date(dbFileInfo.upload_date).toLocaleDateString('en-CA') : 'Not uploaded'}</td>
							<td>${dbFileInfo && dbFileInfo.last_check_date ? new Date(dbFileInfo.last_check_date).toLocaleDateString('en-CA') : 'Not checked'}</td>
							<td>
								<div class="input-group">
									<input type="date" class="form-control form-control-sm stop-date-input"
										value="${dbFileInfo && dbFileInfo.stop_monitor_date ? dbFileInfo.stop_monitor_date.split('T')[0] : ''}"
										data-filename="${file}">
									<button class="btn btn-outline-primary btn-sm update-stop-date" type="button">Update</button>
								</div>
							</td>
							<td>
								${dbFileInfo && dbFileInfo.stop_monitor_date && new Date(dbFileInfo.stop_monitor_date) < new Date().setHours(0,0,0,0)
									? '<span class="badge bg-danger">Expired</span>'
									: `<span class="badge ${(currentStatus === 'In Progress' || currentStatus === 'Checking Status') ? 'bg-primary' : 'bg-secondary'} status-badge" data-filename="${file}">${currentStatus}</span>`}
							</td>
							<td>
								<button class="btn btn-danger btn-sm delete-btn" data-filename="${file}">Delete</button>
							</td>
							<td>
								<button class="btn btn-primary btn-sm upload-amazon-btn" data-filename="${file}">Upload to Amazon</button>
							</td>
							<td>
								<div class="form-check form-switch">
									<input class="form-check-input schedule-upload-checkbox" type="checkbox"
										id="scheduleUpload-${index}" data-filename="${file}"
										${isScheduled ? 'checked' : ''}>
									<label class="form-check-label" for="scheduleUpload-${index}">
										Weekly Upload
									</label>
								</div>
							</td>
						`;
						fileList.appendChild(row);
					});

					// Reattach delete button event listeners
					document.querySelectorAll('.delete-btn').forEach(button => {
						button.addEventListener('click', async () => {
							const filename = button.dataset.filename;
							if (confirm(`Delete ${filename}?`)) {
								try {
									showLoading();
									const response = await fetch(`/delete/${filename}`, {
										method: 'POST'
									});
									const result = await response.json();

									if (response.ok) {
										button.closest('.list-group-item').classList.add('animate__fadeOutRight');
										setTimeout(() => {
											location.reload();
										}, 500);
										showNotification('File deleted successfully');
									} else {
										showNotification(result.error, 'danger');
									}
								} catch (error) {
									showNotification('Error deleting file', 'danger');
								} finally {
									hideLoading();
								}
							}
						});
					});

					// Reattach upload to Amazon button event listeners
					document.querySelectorAll('.upload-amazon-btn').forEach(button => {
						button.addEventListener('click', function() {
							const fileName = this.dataset.filename;
							uploadToAmazon(fileName);
						});
					});

					// Reattach schedule upload checkbox listeners
					document.querySelectorAll('.schedule-upload-checkbox').forEach(checkbox => {
						checkbox.addEventListener('change', function() {
							const fileName = this.dataset.filename;
							const isScheduled = this.checked;

							fetch('/update-schedule-upload', {
								method: 'POST',
								headers: {
									'Content-Type': 'application/json'
								},
								body: JSON.stringify({
									file_name: fileName,
									scheduled: isScheduled
								})
							})
							.then(response => response.json())
							.then(data => {
								if (data.status === 'success') {
									showNotification(isScheduled ?
										`Weekly upload scheduled for ${fileName}` :
										`Weekly upload canceled for ${fileName}`,
										'success');
									// Update local list of scheduled files
									updateScheduledFiles();
								} else {
									showNotification(data.error || 'Failed to update schedule', 'danger');
									// Revert checkbox state
									this.checked = !isScheduled;
								}
							})
							.catch(error => {
								console.error('Error updating schedule:', error);
								showNotification('Error updating schedule', 'danger');
								// Revert checkbox state
								this.checked = !isScheduled;
							});
						});
					});
				} else {
					showNotification(filesResult.error || dbFilesResult.message, 'danger');
				}
			} catch (error) {
				showNotification('Error refreshing files', 'danger');
			} finally {
				hideLoading();
			}
		});

		// Initialize data loading when DOM is ready
		//document.addEventListener('DOMContentLoaded', loadCSVData);

		// File Upload Handler
		document.getElementById('uploadForm').addEventListener('submit', async (e) => {
			e.preventDefault();
			const formData = new FormData();
			const fileInput = document.getElementById('fileInput');
			const submitBtn = e.target.querySelector('button[type="submit"]');
			submitBtn.disabled = true; // Disable the button during upload
			formData.append('file', fileInput.files[0]);

			try {
				const spinner = submitBtn.querySelector('.spinner-border');
				spinner.classList.remove('d-none');
				showLoading();

				const response = await fetch('/upload', {
					method: 'POST',
					body: formData
				});
				const result = await response.json();

				if (response.ok) {
					showNotification('File uploaded successfully');
					location.reload();
				} else {
					showNotification(result.error, 'danger');
				}
			} catch (error) {
				showNotification('Error uploading file', 'danger');
			} finally {
				submitBtn.disabled = false; // Re-enable the button after upload
				const spinner = submitBtn.querySelector('.spinner-border');
				spinner.classList.add('d-none');
				hideLoading();
			}
		});

		document.querySelectorAll('.delete-btn').forEach(button => {
			button.addEventListener('click', async () => {
				const filename = button.dataset.filename;
				if (confirm(`Delete ${filename}?`)) {
					try {
						showLoading();
						const response = await fetch(`/delete/${filename}`, {
							method: 'POST'
						});
						const result = await response.json();

						if (response.ok) {
							button.closest('tr').classList.add('animate__fadeOutRight');
							setTimeout(() => {
								location.reload();
							}, 500);
							showNotification('File deleted successfully');
						} else {
							showNotification(result.error, 'danger');
						}
					} catch (error) {
						showNotification('Error deleting file', 'danger');
					} finally {
						hideLoading();
					}
				}
			});
		});

		// Status Check Handler
		document.getElementById('checkStatus').addEventListener('click', async () => {
			const selectedFiles = Array.from(document.querySelectorAll('.file-checkbox:checked')).map(cb => cb.value);
			const ignoreDate = document.getElementById('ignoreDate').checked;

			if (selectedFiles.length === 0) {
				showNotification('Please select files to check', 'warning');
				return;
			}

			try {
				showLoading();
				const response = await fetch('/status', {
					method: 'POST',
					headers: {
						'Content-Type': 'application/json'
					},
					body: JSON.stringify({
						files: selectedFiles,
						ignore_date: ignoreDate
					})
				});
				if (handleAuthError(response)) {
					return; // Authentication error handled
				}

				const result = await response.json();

				if (response.ok) {
					let message = result.message || 'Status check completed successfully';
					if (result.files_skipped && result.files_skipped.length > 0) {
						message += `\nSkipped files already being checked: ${result.files_skipped.join(', ')}`;
					}
					showNotification(message);
				} else {
					// Handle specific error for files already being checked
					if (result.files_already_checking && result.files_already_checking.length > 0) {
						showNotification(result.message, 'warning');
					} else {
						showNotification(result.error || result.message, 'danger');
					}
				}
			} catch (error) {
				showNotification('Error checking status', 'danger');
			} finally {
				hideLoading();
			}
		});


		// Download Results Handler
		document.getElementById('downloadResults').addEventListener('click', async () => {
			const selectedFiles = Array.from(document.querySelectorAll('.file-checkbox:checked')).map(cb => cb.value);

			try {
				showLoading();
				const response = await fetch('/download', {
					method: 'POST',
					headers: {
						'Content-Type': 'application/json'
					},
					body: JSON.stringify({
						files: selectedFiles
					})
				});

				if (response.ok) {
					const blob = await response.blob();
					const url = window.URL.createObjectURL(blob);
					const a = document.createElement('a');
					a.href = url;
					a.download = 'results.csv';
					document.body.appendChild(a);
					a.click();
					window.URL.revokeObjectURL(url);
					showNotification('Results downloaded successfully');
					//loadCSVData();
				} else {
					const result = await response.json();
					showNotification(result.error, 'danger');
				}
			} catch (error) {
				showNotification('Error downloading results', 'danger');
			} finally {
				hideLoading();
			}
		});

		// Check Valid Parts Handler
		document.getElementById('checkValidParts').addEventListener('click', async () => {
			const selectedFiles = Array.from(document.querySelectorAll('.file-checkbox:checked')).map(cb => cb.value);

			try {
				showLoading();
				const response = await fetch('/check-valid-parts', {
					method: 'POST',
					headers: {
						'Content-Type': 'application/json'
					},
					body: JSON.stringify({
						files: selectedFiles
					})
				});
				const result = await response.json();

				if (response.ok) {
					const stats = result.data;
					const message = `Validation completed: ${stats.valid_parts} valid parts, ${stats.invalid_parts} invalid parts out of ${stats.total_parts} total parts`;
					showNotification(message);
				} else {
					showNotification(result.error, 'danger');
				}
			} catch (error) {
				showNotification('Error checking valid parts', 'danger');
			} finally {
				hideLoading();
			}
		});

		// Stop Monitor Date Update Handler
		document.addEventListener('click', (e) => {
			if (e.target.classList.contains('update-stop-date')) {
				const dateInput = e.target.previousElementSibling;
				const fileName = dateInput.dataset.filename;
				const stopDate = dateInput.value;

				if (!stopDate) {
					showNotification('Please select a date', 'warning');
					return;
				}

				try {
					showLoading();
					fetch('/update-stop-date', {
						method: 'POST',
						headers: {
							'Content-Type': 'application/json'
						},
						body: JSON.stringify({
							file_name: fileName,
							stop_date: stopDate
						})
					})
					.then(response => response.json())
					.then(result => {
						if (result.status === 'success') {
							showNotification('Stop monitor date updated successfully');
						} else {
							showNotification(result.error, 'danger');
						}
					})
					.catch(error => {
						showNotification('Error updating stop monitor date', 'danger');
					})
					.finally(() => {
						hideLoading();
					});
				} catch (error) {
					hideLoading();
					showNotification('Error updating stop monitor date', 'danger');
				}
			}
		});

		// Files table sorting functionality
		let filesTableData = [];
		let currentFilesSortColumn = '';
		let filesSortDirection = 'asc';

		// Initialize the files table data
		document.addEventListener('DOMContentLoaded', function() {
			// Convert existing table rows to data array
			const rows = Array.from(document.getElementById('fileList').getElementsByTagName('tr'));
			filesTableData = rows.map(row => ({
				checkbox: row.cells[0].innerHTML,
				id: row.cells[1].textContent,
				file_name: row.cells[2].querySelector('label').textContent.trim(),
				upload_date: row.cells[3].textContent,
				last_check_date: row.cells[4].textContent,
				stop_monitor_date: row.cells[5].querySelector('input').value,  // Store the actual date value
				stop_monitor_html: row.cells[5].innerHTML,  // Store the complete HTML structure
				status: row.cells[6].querySelector('.badge').textContent,
				actions: row.cells[7].innerHTML,
				amazon: row.cells[8].innerHTML,
				schedule_upload: row.cells[9].innerHTML
			}));

			// Add click handlers for sorting
			const filesTable = document.getElementById('filesTable');
			if (filesTable) {
				filesTable.querySelector('thead').addEventListener('click', function(e) {
					const th = e.target.closest('th');
					if (th && th.dataset.sort) {
						sortFilesTable(th.dataset.sort);
					}
				});
			}
		});

		function sortFilesTable(column) {
			if (currentFilesSortColumn === column) {
				filesSortDirection = filesSortDirection === 'asc' ? 'desc' : 'asc';
			} else {
				currentFilesSortColumn = column;
				filesSortDirection = 'asc';
			}

			filesTableData.sort((a, b) => {
				let valueA = a[column] || '';
				let valueB = b[column] || '';

				// Handle date columns
				if (column.includes('date')) {
					valueA = valueA === 'Not uploaded' || valueA === 'Not checked' ? '' : valueA;
					valueB = valueB === 'Not uploaded' || valueB === 'Not checked' ? '' : valueB;
					if (valueA && valueB) {
						valueA = new Date(valueA);
						valueB = new Date(valueB);
					}
				}

				// Handle ID column as number
				if (column === 'id') {
					valueA = valueA === '-' ? -1 : parseInt(valueA);
					valueB = valueB === '-' ? -1 : parseInt(valueB);
				}

				if (filesSortDirection === 'asc') {
					return valueA > valueB ? 1 : valueA < valueB ? -1 : 0;
				} else {
					return valueA < valueB ? 1 : valueA > valueB ? -1 : 0;
				}
			});

			renderFilesTable();
		}

		function renderFilesTable() {
			const tbody = document.getElementById('fileList');
			tbody.innerHTML = '';
			const today = new Date().setHours(0, 0, 0, 0);

			filesTableData.forEach((row, index) => {
				const tr = document.createElement('tr');
				tr.className = 'animate__animated animate__fadeIn';

				// Create file name with conditional expired class
				const fileNameClass = row.stop_monitor_date && new Date(row.stop_monitor_date).setHours(0, 0, 0, 0) < today ? 'expired-file' : '';

				tr.innerHTML = `
					<td>
						<input class="form-check-input file-checkbox" type="checkbox" value="${row.file_name}" id="file-${index}">
					</td>
					<td>${row.id}</td>
					<td>
						<label class="form-check-label ${fileNameClass}" for="file-${index}">${row.file_name}</label>
					</td>
					<td>${row.upload_date}</td>
					<td>${row.last_check_date}</td>
					<td>
						<div class="input-group">
							<input type="date" class="form-control form-control-sm stop-date-input"
								value="${row.stop_monitor_date || ''}"
								data-filename="${row.file_name}">
							<button class="btn btn-outline-primary btn-sm update-stop-date" type="button">Update</button>
						</div>
					</td>
					<td>
						${row.stop_monitor_date && new Date(row.stop_monitor_date).setHours(0, 0, 0, 0) < today
							? '<span class="badge bg-danger">Expired</span>'
							: `<span class="badge bg-secondary status-badge" data-filename="${row.file_name}">Idle</span>`}
					</td>
					<td>${row.actions}</td>
					<td>${row.amazon}</td>
					<td>
						<div class="form-check form-switch">
							<input class="form-check-input schedule-upload-checkbox" type="checkbox"
								id="scheduleUpload-${index}" data-filename="${row.file_name}"
								${scheduledFiles.includes(row.file_name) ? 'checked' : ''}>
							<label class="form-check-label" for="scheduleUpload-${index}">
								Weekly Upload
							</label>
						</div>
					</td>
				`;
				tbody.appendChild(tr);
			});

			// Update sort icons
			document.querySelectorAll('th[data-sort] .sort-icon').forEach(icon => {
				icon.textContent = '↕';
			});
			if (currentFilesSortColumn) {
				const currentHeader = document.querySelector(`th[data-sort="${currentFilesSortColumn}"] .sort-icon`);
				if (currentHeader) {
					currentHeader.textContent = filesSortDirection === 'asc' ? '↑' : '↓';
				}
			}

			// Reattach event listeners
			reattachEventListeners();
		}

		function reattachEventListeners() {
			// Reattach update-stop-date button listeners
			document.querySelectorAll('.update-stop-date').forEach(button => {
				button.addEventListener('click', function() {
					const input = this.previousElementSibling;
					const fileName = input.dataset.filename;
					const newDate = input.value;
					// Your existing update stop date logic here
					updateStopMonitorDate(fileName, newDate);
				});
			});

			// Reattach any other necessary event listeners
			document.querySelectorAll('.delete-btn').forEach(button => {

				button.addEventListener('click', async () => {
				const filename = button.dataset.filename;
				if (confirm(`Delete ${filename}?`)) {
					try {
						showLoading();
						const response = await fetch(`/delete/${filename}`, {
							method: 'POST'
						});
						const result = await response.json();

						if (response.ok) {
							button.closest('tr').classList.add('animate__fadeOutRight');
							setTimeout(() => {
								location.reload();
							}, 500);
							showNotification('File deleted successfully');
						} else {
							showNotification(result.error, 'danger');
						}
					} catch (error) {
						showNotification('Error deleting file', 'danger');
					} finally {
						hideLoading();
					}
				}
			});
			});

			document.querySelectorAll('.upload-amazon-btn').forEach(button => {
				button.addEventListener('click', function() {
					const fileName = this.dataset.filename;
					// Your existing upload to Amazon logic here
					uploadToAmazon(fileName);
				});
			});

			// Reattach schedule upload checkbox listeners
			document.querySelectorAll('.schedule-upload-checkbox').forEach(checkbox => {
				checkbox.addEventListener('change', function() {
					const fileName = this.dataset.filename;
					const isScheduled = this.checked;

					fetch('/update-schedule-upload', {
						method: 'POST',
						headers: {
							'Content-Type': 'application/json'
						},
						body: JSON.stringify({
							file_name: fileName,
							scheduled: isScheduled
						})
					})
					.then(response => response.json())
					.then(data => {
						if (data.status === 'success') {
							showNotification(isScheduled ?
								`Weekly upload scheduled for ${fileName}` :
								`Weekly upload canceled for ${fileName}`,
								'success');
							// Update local list of scheduled files
							updateScheduledFiles();
						} else {
							showNotification(data.error || 'Failed to update schedule', 'danger');
							// Revert checkbox state
							this.checked = !isScheduled;
						}
					})
					.catch(error => {
						console.error('Error updating schedule:', error);
						showNotification('Error updating schedule', 'danger');
						// Revert checkbox state
						this.checked = !isScheduled;
					});
				});
			});
		}
	</script>
	<script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/js/all.min.js"></script>
</body>
</html>







